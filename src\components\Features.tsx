import Image from 'next/image';
import { WhatsAppIcon, ClockIcon, ChartIcon } from './Icons';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  isRight?: boolean;
}

function FeatureCard({ icon, title, description, isRight }: FeatureCardProps) {
  return (
    <div className={`flex flex-col ${isRight ? 'md:flex-row-reverse' : 'md:flex-row'} items-center gap-6`}>
      <div className="flex-shrink-0 p-4 bg-ComidaFacil-primary bg-opacity-10 rounded-full">
        <div className="w-12 h-12 flex items-center justify-center text-ComidaFacil-primary">
          {icon}
        </div>
      </div>
      <div>
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </div>
    </div>
  );
}

export function FeaturesSection() {
  return (
    <section id="features" className="py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">
            Funcionalidades que transformam seu negócio
          </h2>
          <p className="text-gray-600 max-w-3xl mx-auto">
            O ComidaFacil oferece todas as ferramentas que você precisa para automatizar seu delivery 
            e proporcionar uma experiência incrível para seus clientes.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div className="order-2 lg:order-1">
            <div className="space-y-10">
              <FeatureCard
                icon={<WhatsAppIcon />}
                title="Automatização de Atendimento"
                description="Receba e processe pedidos automaticamente via WhatsApp, sem necessidade de operadores. Respostas instantâneas 24/7 para seus clientes."
              />
              <FeatureCard
                icon={<ClockIcon />}
                title="Cardápio Digital Interativo"
                description="Cardápio digital completo com fotos, descrições e preços atualizados. Seus clientes podem fazer pedidos diretamente pelo WhatsApp."
              />
              <FeatureCard
                icon={<ChartIcon />}
                title="Relatórios e Analytics"
                description="Acompanhe o desempenho do seu negócio com relatórios detalhados. Analise vendas, itens mais populares e horários de pico."
              />
            </div>
          </div>
          <div className="order-1 lg:order-2 flex justify-center">
            <div className="relative w-full max-w-md h-[500px]">
              <Image
                src="/assets/smartphone-mockup.png"
                alt="Smartphone com WhatsApp mostrando o ComidaFacil em ação"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>

        <div className="mt-24 grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="bg-white p-8 rounded-lg shadow-md border border-gray-100">
            <div className="p-4 bg-ComidaFacil-primary bg-opacity-10 rounded-full inline-block mb-4">
              <svg className="w-8 h-8 text-ComidaFacil-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Rápido e Eficiente</h3>
            <p className="text-gray-600">
              Reduza o tempo de atendimento e entrega com processos automáticos e fluxos otimizados para delivery.
            </p>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md border border-gray-100">
            <div className="p-4 bg-ComidaFacil-primary bg-opacity-10 rounded-full inline-block mb-4">
              <svg className="w-8 h-8 text-ComidaFacil-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Seguro e Confiável</h3>
            <p className="text-gray-600">
              Proteja os dados dos seus clientes e garanta a integridade das transações com nossa plataforma segura.
            </p>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md border border-gray-100">
            <div className="p-4 bg-ComidaFacil-primary bg-opacity-10 rounded-full inline-block mb-4">
              <svg className="w-8 h-8 text-ComidaFacil-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Escalável e Adaptável</h3>
            <p className="text-gray-600">
              Cresça sem preocupações. Nossa plataforma se adapta ao tamanho do seu negócio, do pequeno restaurante à grande rede.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
} 