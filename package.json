{"name": "ComidaFacil", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^5.10.0", "@whiskeysockets/baileys": "^6.5.0", "next": "14.1.0", "next-auth": "^4.24.5", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}, "devDependencies": {"@types/node": "^20.17.24", "@types/react": "^18.3.18", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.35", "prisma": "^5.10.0"}}