import Link from 'next/link';
import { CheckIcon } from './Icons';

interface PlanFeature {
  text: string;
  available: boolean;
}

interface PricingPlanProps {
  name: string;
  price: string;
  description: string;
  features: PlanFeature[];
  isPopular?: boolean;
  ctaText: string;
  ctaLink: string;
}

export function PricingPlan({ 
  name, 
  price, 
  description, 
  features, 
  isPopular = false,
  ctaText,
  ctaLink 
}: PricingPlanProps) {
  return (
    <div className={`
      bg-white rounded-xl shadow-lg overflow-hidden transition-transform hover:scale-105
      ${isPopular ? 'border-2 border-ComidaFacil-primary' : 'border border-gray-200'}
    `}>
      {isPopular && (
        <div className="bg-ComidaFacil-primary text-white text-center py-2 text-sm font-medium">
          Mais Popular
        </div>
      )}
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-1">{name}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <div className="mb-6">
          <span className="text-4xl font-bold text-gray-900">{price}</span>
          {price !== 'Grátis' && <span className="text-gray-600">/mês</span>}
        </div>
        <ul className="space-y-3 mb-6">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <span className={`mr-2 ${feature.available ? 'text-ComidaFacil-primary' : 'text-gray-400'}`}>
                <CheckIcon />
              </span>
              <span className={feature.available ? 'text-gray-700' : 'text-gray-400'}>
                {feature.text}
              </span>
            </li>
          ))}
        </ul>
        <Link
          href={ctaLink}
          className={`
            block w-full text-center py-3 rounded-lg font-medium transition-colors
            ${isPopular 
              ? 'bg-ComidaFacil-primary text-white hover:bg-ComidaFacil-secondary' 
              : 'bg-white text-ComidaFacil-primary border border-ComidaFacil-primary hover:bg-ComidaFacil-primary hover:text-white'
            }
          `}
        >
          {ctaText}
        </Link>
      </div>
    </div>
  );
}

export function PricingSection() {
  const plans = [
    {
      name: "Inicial",
      price: "Grátis",
      description: "Perfeito para testar a plataforma",
      features: [
        { text: "Até 50 pedidos/mês", available: true },
        { text: "Cardápio digital básico", available: true },
        { text: "WhatsApp integrado", available: true },
        { text: "Dashboard simples", available: true },
        { text: "Suporte por email", available: true },
        { text: "Personalização limitada", available: false },
        { text: "Múltiplos administradores", available: false },
        { text: "API para integração", available: false },
      ],
      ctaText: "Começar Grátis",
      ctaLink: "/register",
    },
    {
      name: "Profissional",
      price: "R$ 99",
      description: "Para negócios em crescimento",
      features: [
        { text: "Pedidos ilimitados", available: true },
        { text: "Cardápio digital completo", available: true },
        { text: "WhatsApp integrado", available: true },
        { text: "Dashboard completo", available: true },
        { text: "Suporte prioritário", available: true },
        { text: "Personalização total", available: true },
        { text: "Múltiplos administradores", available: true },
        { text: "API para integração", available: false },
      ],
      isPopular: true,
      ctaText: "Assinar Agora",
      ctaLink: "/register?plan=pro",
    },
    {
      name: "Empresarial",
      price: "R$ 249",
      description: "Para redes e franquias",
      features: [
        { text: "Pedidos ilimitados", available: true },
        { text: "Cardápio digital premium", available: true },
        { text: "WhatsApp integrado", available: true },
        { text: "Dashboard avançado", available: true },
        { text: "Suporte 24/7 dedicado", available: true },
        { text: "Personalização total", available: true },
        { text: "Múltiplos administradores", available: true },
        { text: "API para integração", available: true },
      ],
      ctaText: "Contato Comercial",
      ctaLink: "/contact",
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-4">
          Planos Simples e Transparentes
        </h2>
        <p className="text-gray-600 text-center mb-12 max-w-3xl mx-auto">
          Escolha o plano ideal para o seu negócio e comece a automatizar seu delivery hoje mesmo
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <PricingPlan
              key={index}
              name={plan.name}
              price={plan.price}
              description={plan.description}
              features={plan.features}
              isPopular={plan.isPopular}
              ctaText={plan.ctaText}
              ctaLink={plan.ctaLink}
            />
          ))}
        </div>
      </div>
    </section>
  );
} 