import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET() {
  try {
    // TODO: Adicionar autenticação e pegar o restaurantId do usuário logado
    const restaurantId = 'test-restaurant-id'

    const menuItems = await prisma.menuItem.findMany({
      where: { restaurantId },
      orderBy: { category: 'asc' }
    })

    return NextResponse.json(menuItems)
  } catch (error) {
    console.error('Error fetching menu items:', error)
    return NextResponse.json(
      { error: 'Failed to fetch menu items' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    // TODO: Adicionar autenticação e pegar o restaurantId do usuário logado
    const restaurantId = 'test-restaurant-id'
    const data = await request.json()

    const menuItem = await prisma.menuItem.create({
      data: {
        ...data,
        restaurantId
      }
    })

    return NextResponse.json(menuItem)
  } catch (error) {
    console.error('Error creating menu item:', error)
    return NextResponse.json(
      { error: 'Failed to create menu item' },
      { status: 500 }
    )
  }
}

export async function PUT(request: Request) {
  try {
    const data = await request.json()
    const { id, ...updateData } = data

    if (!id) {
      return NextResponse.json(
        { error: 'Menu item ID is required' },
        { status: 400 }
      )
    }

    const menuItem = await prisma.menuItem.update({
      where: { id },
      data: updateData
    })

    return NextResponse.json(menuItem)
  } catch (error) {
    console.error('Error updating menu item:', error)
    return NextResponse.json(
      { error: 'Failed to update menu item' },
      { status: 500 }
    )
  }
} 