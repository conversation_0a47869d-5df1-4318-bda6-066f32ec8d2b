'use client'

import { useState, useEffect } from 'react'

interface Order {
  id: string
  customerName: string
  total: number
  status: string
  createdAt: string
}

interface Stats {
  totalOrders: number
  totalRevenue: number
  pendingOrders: number
}

export default function Dashboard() {
  const [orders, setOrders] = useState<Order[]>([])
  const [stats, setStats] = useState<Stats>({
    totalOrders: 0,
    totalRevenue: 0,
    pendingOrders: 0,
  })

  useEffect(() => {
    fetchOrders()
    fetchStats()
  }, [])

  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/orders')
      const data = await response.json()
      setOrders(data)
    } catch (error) {
      console.error('Error fetching orders:', error)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/stats')
      const data = await response.json()
      setStats(data)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const updateOrderStatus = async (orderId: string, status: string) => {
    try {
      await fetch(`/api/orders/${orderId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })
      fetchOrders()
    } catch (error) {
      console.error('Error updating order status:', error)
    }
  }

  return (
    <div className="min-h-screen bg-ComidaFacil-background py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Dashboard</h1>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="card">
            <h3 className="text-lg font-semibold mb-2">Total de Pedidos</h3>
            <p className="text-3xl font-bold text-ComidaFacil-primary">
              {stats.totalOrders}
            </p>
          </div>
          <div className="card">
            <h3 className="text-lg font-semibold mb-2">Receita Total</h3>
            <p className="text-3xl font-bold text-ComidaFacil-primary">
              R$ {stats.totalRevenue.toFixed(2)}
            </p>
          </div>
          <div className="card">
            <h3 className="text-lg font-semibold mb-2">Pedidos Pendentes</h3>
            <p className="text-3xl font-bold text-ComidaFacil-primary">
              {stats.pendingOrders}
            </p>
          </div>
        </div>

        {/* Orders Table */}
        <div className="card">
          <h2 className="text-xl font-semibold mb-4">Pedidos Recentes</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">ID</th>
                  <th className="text-left py-2">Cliente</th>
                  <th className="text-left py-2">Total</th>
                  <th className="text-left py-2">Status</th>
                  <th className="text-left py-2">Data</th>
                  <th className="text-left py-2">Ações</th>
                </tr>
              </thead>
              <tbody>
                {orders.map((order) => (
                  <tr key={order.id} className="border-b">
                    <td className="py-2">#{order.id.slice(-6)}</td>
                    <td className="py-2">{order.customerName}</td>
                    <td className="py-2">R$ {order.total.toFixed(2)}</td>
                    <td className="py-2">
                      <span className={`px-2 py-1 rounded-full text-sm ${
                        order.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                        order.status === 'CONFIRMED' ? 'bg-blue-100 text-blue-800' :
                        order.status === 'PREPARING' ? 'bg-purple-100 text-purple-800' :
                        order.status === 'READY' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {order.status}
                      </span>
                    </td>
                    <td className="py-2">
                      {new Date(order.createdAt).toLocaleDateString()}
                    </td>
                    <td className="py-2">
                      <select
                        className="input py-1"
                        value={order.status}
                        onChange={(e) => updateOrderStatus(order.id, e.target.value)}
                      >
                        <option value="PENDING">Pendente</option>
                        <option value="CONFIRMED">Confirmado</option>
                        <option value="PREPARING">Preparando</option>
                        <option value="READY">Pronto</option>
                        <option value="DELIVERING">Em Entrega</option>
                        <option value="DELIVERED">Entregue</option>
                        <option value="CANCELLED">Cancelado</option>
                      </select>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
} 