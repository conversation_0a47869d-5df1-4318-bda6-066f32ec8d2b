@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 245, 245, 245;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
}

@layer components {
  .btn-primary {
    @apply bg-ComidaFacil-primary text-white px-4 py-2 rounded-lg hover:bg-ComidaFacil-secondary transition-colors;
  }
  
  .btn-secondary {
    @apply bg-white text-ComidaFacil-primary border-2 border-ComidaFacil-primary px-4 py-2 rounded-lg hover:bg-ComidaFacil-primary hover:text-white transition-colors;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  
  .input {
    @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ComidaFacil-primary;
  }
} 