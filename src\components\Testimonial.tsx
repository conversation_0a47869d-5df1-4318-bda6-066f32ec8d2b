import Image from 'next/image';

interface TestimonialProps {
  text: string;
  name: string;
  role: string;
  company: string;
  imageUrl: string;
}

export function TestimonialCard({ text, name, role, company, imageUrl }: TestimonialProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex items-center mb-4">
        <div className="relative w-12 h-12 rounded-full overflow-hidden mr-4">
          <Image
            src={imageUrl}
            alt={`Foto de ${name}`}
            fill
            sizes="(max-width: 48px) 100vw, 48px"
            className="object-cover"
          />
        </div>
        <div>
          <h4 className="font-medium text-gray-900">{name}</h4>
          <p className="text-sm text-gray-600">{role}, {company}</p>
        </div>
      </div>
      <p className="text-gray-700 italic">{text}</p>
      <div className="mt-4 flex text-ComidaFacil-primary">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg 
            key={star} 
            className="w-5 h-5 fill-current" 
            viewBox="0 0 24 24"
          >
            <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" />
          </svg>
        ))}
      </div>
    </div>
  );
}

export function TestimonialSection() {
  const testimonials = [
    {
      id: 1,
      text: "O ComidaFacil revolucionou nosso delivery. Aumentamos as vendas em 40% no primeiro mês de uso!",
      name: "Ana Silva",
      role: "Proprietária",
      company: "Sabor Caseiro",
      imageUrl: "https://randomuser.me/api/portraits/women/12.jpg"
    },
    {
      id: 2,
      text: "Interface super intuitiva e atendimento automático perfeito. Nossos clientes adoraram a facilidade de fazer pedidos.",
      name: "Carlos Oliveira",
      role: "Gerente",
      company: "Pizza Express",
      imageUrl: "https://randomuser.me/api/portraits/men/32.jpg"
    },
    {
      id: 3,
      text: "Conseguimos reduzir erros nos pedidos e melhorar o tempo de entrega. O suporte da equipe ComidaFacil é excelente!",
      name: "Mariana Santos",
      role: "Coordenadora",
      company: "Sushi Delivery",
      imageUrl: "https://randomuser.me/api/portraits/women/45.jpg"
    },
  ];

  return (
    <section id="testimonials" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-4">
          O que nossos clientes dizem
        </h2>
        <p className="text-gray-600 text-center mb-12 max-w-3xl mx-auto">
          Centenas de restaurantes e estabelecimentos já estão aumentando suas vendas e melhorando seu atendimento com o ComidaFacil.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <TestimonialCard 
              key={testimonial.id}
              text={testimonial.text}
              name={testimonial.name}
              role={testimonial.role}
              company={testimonial.company}
              imageUrl={testimonial.imageUrl}
            />
          ))}
        </div>
      </div>
    </section>
  );
} 