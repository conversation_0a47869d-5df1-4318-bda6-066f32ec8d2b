'use client';

import { useState } from 'react';

interface FaqItemProps {
  question: string;
  answer: string;
}

export function FaqItem({ question, answer }: FaqItemProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200 py-4">
      <button
        className="flex justify-between items-center w-full text-left focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="font-medium text-gray-900">{question}</span>
        <svg
          className={`w-5 h-5 text-ComidaFacil-primary transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      <div
        className={`mt-2 text-gray-600 overflow-hidden transition-all duration-300 ${
          isOpen ? 'max-h-96' : 'max-h-0'
        }`}
      >
        <p className="py-2">{answer}</p>
      </div>
    </div>
  );
}

export function FaqSection() {
  const faqs = [
    {
      id: 1,
      question: 'O que é o ComidaFacil?',
      answer: 'ComidaFacil é uma plataforma SaaS (Software as a Service) que automatiza o processo de vendas e atendimento via WhatsApp para estabelecimentos de delivery. Oferecemos uma solução completa que inclui cardápio digital, gestão de pedidos, e atendimento automatizado.'
    },
    {
      id: 2,
      question: 'Como funciona a integração com WhatsApp?',
      answer: 'A integração é simples e rápida. Após o cadastro, você fará a leitura de um QR code para conectar sua conta WhatsApp Business à plataforma. Nosso sistema utiliza a biblioteca Baileys para uma comunicação segura e estável com a API do WhatsApp.'
    },
    {
      id: 3,
      question: 'Quanto custa usar o ComidaFacil?',
      answer: 'Oferecemos diferentes planos de assinatura, começando com um plano gratuito limitado e planos pagos a partir de R$ 99/mês. O valor exato depende do tamanho do seu negócio e do volume de pedidos. Entre em contato com nossa equipe para uma cotação personalizada.'
    },
    {
      id: 4,
      question: 'É necessário ter conhecimento técnico para usar?',
      answer: 'Não! O ComidaFacil foi desenvolvido pensando em ser extremamente fácil de usar. Nossa interface é intuitiva e oferecemos um processo de onboarding guiado. Além disso, nossa equipe de suporte está sempre disponível para ajudar.'
    },
    {
      id: 5,
      question: 'O sistema pode ser personalizado para meu negócio?',
      answer: 'Sim! Você pode personalizar o cardápio, as mensagens automáticas, e até mesmo a experiência do cliente. Também oferecemos integrações com sistemas populares de gestão e entrega.'
    },
    {
      id: 6,
      question: 'Como são processados os pagamentos?',
      answer: 'O ComidaFacil oferece integração com diversas opções de pagamento, incluindo PIX, cartões de crédito/débito, e pagamento na entrega. Você escolhe quais métodos deseja disponibilizar para seus clientes.'
    }
  ];

  return (
    <section id="faq" className="py-20">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-4">
          Perguntas Frequentes
        </h2>
        <p className="text-gray-600 text-center mb-12 max-w-3xl mx-auto">
          Tire suas dúvidas sobre o ComidaFacil e descubra como podemos ajudar seu negócio
        </p>
        <div className="max-w-3xl mx-auto">
          {faqs.map((faq) => (
            <FaqItem 
              key={faq.id} 
              question={faq.question} 
              answer={faq.answer} 
            />
          ))}
        </div>
      </div>
    </section>
  );
} 