import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET() {
  try {
    // TODO: Adicionar autenticação e pegar o restaurantId do usuário logado
    const restaurantId = 'test-restaurant-id'

    const [
      totalOrders,
      totalRevenue,
      pendingOrders
    ] = await Promise.all([
      // Total de pedidos
      prisma.order.count({
        where: { restaurantId }
      }),
      
      // Receita total
      prisma.order.aggregate({
        where: { 
          restaurantId,
          status: {
            notIn: ['CANCELLED']
          }
        },
        _sum: {
          total: true
        }
      }),
      
      // Pedidos pendentes
      prisma.order.count({
        where: { 
          restaurantId,
          status: 'PENDING'
        }
      })
    ])

    return NextResponse.json({
      totalOrders,
      totalRevenue: totalRevenue._sum.total || 0,
      pendingOrders
    })
  } catch (error) {
    console.error('Error fetching stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    )
  }
} 