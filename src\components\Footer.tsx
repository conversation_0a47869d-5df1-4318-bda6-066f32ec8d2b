import Link from 'next/link';
import Image from 'next/image';

export default function Footer() {
  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-1">
            <Link href="/" className="flex items-center space-x-2 mb-4">
              <Image 
                src="/assets/logo.png" 
                alt="ComidaFacil Logo" 
                width={40} 
                height={40} 
                className="w-10 h-10"
              />
              <span className="text-xl font-bold text-ComidaFacil-primary">ComidaFacil</span>
            </Link>
            <p className="text-gray-600 mb-4">
              Automatize suas vendas de delivery via WhatsApp e aumente seu faturamento.
            </p>
            <div className="flex space-x-4">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                <svg className="w-6 h-6 text-gray-500 hover:text-ComidaFacil-primary transition-colors" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22 12.0611C22 6.50451 17.5229 2 12 2C6.47715 2 2 6.50451 2 12.0611C2 17.0828 5.65684 21.2452 10.4375 22V14.9694H7.89844V12.0611H10.4375V9.84452C10.4375 7.32296 11.9305 5.93012 14.2146 5.93012C15.3084 5.93012 16.4531 6.12663 16.4531 6.12663V8.60263H15.1922C13.95 8.60263 13.5625 9.37818 13.5625 10.1747V12.0611H16.3359L15.8926 14.9694H13.5625V22C18.3432 21.2452 22 17.0828 22 12.0611Z" />
                </svg>
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                <svg className="w-6 h-6 text-gray-500 hover:text-ComidaFacil-primary transition-colors" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2C14.717 2 15.056 2.01 16.122 2.06C17.187 2.11 17.912 2.277 18.55 2.525C19.21 2.779 19.766 3.123 20.322 3.678C20.8305 4.1779 21.224 4.78259 21.475 5.45C21.722 6.087 21.89 6.813 21.94 7.878C21.987 8.944 22 9.283 22 12C22 14.717 21.99 15.056 21.94 16.122C21.89 17.187 21.722 17.912 21.475 18.55C21.2247 19.2178 20.8311 19.8226 20.322 20.322C19.822 20.8303 19.2173 21.2238 18.55 21.475C17.913 21.722 17.187 21.89 16.122 21.94C15.056 21.987 14.717 22 12 22C9.283 22 8.944 21.99 7.878 21.94C6.813 21.89 6.088 21.722 5.45 21.475C4.78233 21.2245 4.17753 20.8309 3.678 20.322C3.16941 19.8222 2.77593 19.2175 2.525 18.55C2.277 17.913 2.11 17.187 2.06 16.122C2.013 15.056 2 14.717 2 12C2 9.283 2.01 8.944 2.06 7.878C2.11 6.812 2.277 6.088 2.525 5.45C2.77524 4.78218 3.1688 4.17732 3.678 3.678C4.17767 3.16923 4.78243 2.77573 5.45 2.525C6.088 2.277 6.812 2.11 7.878 2.06C8.944 2.013 9.283 2 12 2ZM12 7C10.6739 7 9.40215 7.52678 8.46447 8.46447C7.52678 9.40215 7 10.6739 7 12C7 13.3261 7.52678 14.5979 8.46447 15.5355C9.40215 16.4732 10.6739 17 12 17C13.3261 17 14.5979 16.4732 15.5355 15.5355C16.4732 14.5979 17 13.3261 17 12C17 10.6739 16.4732 9.40215 15.5355 8.46447C14.5979 7.52678 13.3261 7 12 7ZM18.5 6.75C18.5 6.41848 18.3683 6.10054 18.1339 5.86612C17.8995 5.6317 17.5815 5.5 17.25 5.5C16.9185 5.5 16.6005 5.6317 16.3661 5.86612C16.1317 6.10054 16 6.41848 16 6.75C16 7.08152 16.1317 7.39946 16.3661 7.63388C16.6005 7.8683 16.9185 8 17.25 8C17.5815 8 17.8995 7.8683 18.1339 7.63388C18.3683 7.39946 18.5 7.08152 18.5 6.75ZM12 9C12.7956 9 13.5587 9.31607 14.1213 9.87868C14.6839 10.4413 15 11.2044 15 12C15 12.7956 14.6839 13.5587 14.1213 14.1213C13.5587 14.6839 12.7956 15 12 15C11.2044 15 10.4413 14.6839 9.87868 14.1213C9.31607 13.5587 9 12.7956 9 12C9 11.2044 9.31607 10.4413 9.87868 9.87868C10.4413 9.31607 11.2044 9 12 9Z" />
                </svg>
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" aria-label="Twitter">
                <svg className="w-6 h-6 text-gray-500 hover:text-ComidaFacil-primary transition-colors" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19.633 7.99701C19.646 8.17201 19.646 8.34601 19.646 8.52001C19.646 13.845 15.593 19.981 8.186 19.981C5.904 19.981 3.784 19.32 2 18.172C2.324 18.209 2.636 18.222 2.973 18.222C4.856 18.222 6.589 17.586 7.974 16.501C6.203 16.464 4.719 15.306 4.207 13.708C4.456 13.745 4.706 13.77 4.968 13.77C5.329 13.77 5.692 13.72 6.029 13.633C4.182 13.259 2.799 11.638 2.799 9.68001V9.63001C3.336 9.92901 3.959 10.116 4.619 10.141C3.534 9.41901 2.823 8.18401 2.823 6.78701C2.823 6.03901 3.022 5.35301 3.371 4.75501C5.354 7.19801 8.335 8.79501 11.677 8.97001C11.615 8.67001 11.577 8.35901 11.577 8.04701C11.577 5.82701 13.373 4.01901 15.605 4.01901C16.765 4.01901 17.812 4.50501 18.548 5.29101C19.458 5.11601 20.33 4.77901 21.104 4.31801C20.805 5.25301 20.168 6.03901 19.333 6.53801C20.144 6.45001 20.93 6.22601 21.652 5.91401C21.104 6.71201 20.419 7.42301 19.633 7.99701Z" />
                </svg>
              </a>
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                <svg className="w-6 h-6 text-gray-500 hover:text-ComidaFacil-primary transition-colors" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18.335 18.339H15.67V14.162C15.67 13.166 15.65 11.884 14.28 11.884C12.891 11.884 12.679 12.968 12.679 14.089V18.339H10.013V9.75H12.573V10.92H12.608C12.966 10.246 13.836 9.533 15.136 9.533C17.836 9.533 18.336 11.311 18.336 13.624V18.339H18.335ZM7.003 8.575C6.79956 8.57526 6.59806 8.53537 6.41006 8.45761C6.22207 8.37984 6.05127 8.26574 5.90746 8.12184C5.76365 7.97793 5.64965 7.80706 5.57201 7.61901C5.49437 7.43097 5.4546 7.22944 5.455 7.026C5.4552 6.71983 5.54618 6.4206 5.71644 6.16615C5.8867 5.91169 6.12859 5.71343 6.414 5.60295C6.69941 5.49247 7.01486 5.47633 7.31033 5.55658C7.60579 5.63682 7.86466 5.8096 8.04891 6.04858C8.23317 6.28756 8.33382 6.57791 8.33438 6.87908C8.33494 7.18025 8.23538 7.47096 8.05195 7.7106C7.86852 7.95023 7.61022 8.12393 7.315 8.2052C7.01978 8.28647 6.7042 8.27145 6.418 8.16205C6.36287 8.14158 6.30927 8.11626 6.25767 8.08632H6.25662L6.25567 8.08733L6.25567 8.08733L6.25662 8.08632C6.20065 8.05444 6.14745 8.01745 6.09767 7.97587V7.975L6.09572 7.97404L6.09378 7.97308L6.09194 7.97106L6.09 7.97009C6.03643 7.92532 5.9878 7.87498 5.94467 7.82033C5.90154 7.76568 5.86431 7.70708 5.83333 7.64587L5.83138 7.64195C5.80039 7.58074 5.77613 7.51669 5.75867 7.45033C5.74121 7.38398 5.73033 7.31598 5.72633 7.24777C5.72233 7.17956 5.72567 7.11061 5.73567 7.04233V7.04138C5.74667 6.97028 5.76538 6.90112 5.79167 6.83233C5.81749 6.76391 5.85032 6.69841 5.88933 6.63638C5.92977 6.57156 5.97657 6.51118 6.02918 6.45622C6.08178 6.40126 6.14092 6.35186 6.20533 6.30848C6.26974 6.2651 6.33893 6.22784 6.41167 6.19733C6.55633 6.13433 6.71267 6.10133 6.87033 6.10133C7.02033 6.10133 7.16867 6.13067 7.309 6.18767L7.31 6.18867C7.43028 6.23298 7.54282 6.29569 7.64333 6.37433L7.6495 6.37933L7.65667 6.38533C7.74667 6.46333 7.82333 6.551 7.885 6.648L7.88833 6.65333L7.89167 6.65867C7.94886 6.75376 7.99183 6.85586 8.02 6.96233H8.02102C8.04932 7.06758 8.06379 7.17603 8.06417 7.285C8.06433 7.39158 8.05033 7.4975 8.02233 7.599C7.99432 7.7005 7.9533 7.79883 7.9 7.894C7.84667 7.989 7.78 8.07833 7.702 8.15833C7.623 8.239 7.53467 8.306 7.43433 8.358C7.33483 8.41039 7.22546 8.44387 7.113 8.458L7.11106 8.458C7.07586 8.46467 7.03995 8.46933 7.003 8.471L7.003 8.575ZM7.34933 8.562L7.347 8.56133L7.34467 8.56067C7.452 8.5155 7.54767 8.456 7.62733 8.379L7.62938 8.37695C7.707 8.30095 7.76733 8.21033 7.81033 8.108C7.854 8.00511 7.87826 7.89424 7.87958 7.78252C7.87967 7.39639 7.70667 7.09639 7.45933 6.92838C7.33367 6.84588 7.161 6.77738 6.965 6.77738C6.77 6.77738 6.59833 6.84487 6.474 6.92538L6.47195 6.92644C6.39226 6.9735 6.32178 7.03437 6.26467 7.10638C6.20705 7.17908 6.16509 7.26182 6.14133 7.35038C6.11779 7.43865 6.11107 7.53 6.12138 7.61928C6.13169 7.70855 6.15869 7.79354 6.20067 7.87005C6.24266 7.94656 6.29873 8.01304 6.36533 8.06538C6.43322 8.11894 6.51049 8.15738 6.59267 8.17838C6.67594 8.19962 6.76216 8.20298 6.84667 8.18838L6.85105 8.18732C6.85105 8.18732 6.85306 8.18732 6.855 8.18633L6.85711 8.18527H6.85905C7.02133 8.149 7.189 8.085 7.34933 7.97833V8.562ZM7.07867 17.339H4.40133V9.749H7.07867V17.339Z" />
                </svg>
              </a>
            </div>
          </div>

          {/* Links */}
          <div className="col-span-1">
            <h3 className="text-sm font-bold text-gray-900 uppercase tracking-wider mb-4">Produto</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/#features" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Funcionalidades
                </Link>
              </li>
              <li>
                <Link href="/#pricing" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Preços
                </Link>
              </li>
              <li>
                <Link href="/#testimonials" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Depoimentos
                </Link>
              </li>
              <li>
                <Link href="/#faq" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  FAQ
                </Link>
              </li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-sm font-bold text-gray-900 uppercase tracking-wider mb-4">Suporte</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/help" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Central de Ajuda
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Contato
                </Link>
              </li>
              <li>
                <Link href="/documentation" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Documentação
                </Link>
              </li>
              <li>
                <Link href="/status" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Status
                </Link>
              </li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-sm font-bold text-gray-900 uppercase tracking-wider mb-4">Legal</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/privacy" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Política de Privacidade
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Termos de Uso
                </Link>
              </li>
              <li>
                <Link href="/cookies" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Política de Cookies
                </Link>
              </li>
              <li>
                <Link href="/compliance" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                  Compliance
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-gray-500 text-sm text-center">
            &copy; {new Date().getFullYear()} ComidaFacil. Todos os direitos reservados.
          </p>
        </div>
      </div>
    </footer>
  );
} 