'use client';

import Image from 'next/image';
import Link from 'next/link';
import { FormEvent, useState } from 'react';

export function Hero() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    // Simulação de envio - Em produção, conectar a API
    setTimeout(() => {
      setLoading(false);
      setSuccess(true);
      setEmail('');
      
      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    }, 1000);
  };

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-ComidaFacil-primary to-ComidaFacil-secondary text-white">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute right-0 bottom-0">
          <svg width="600" height="600" viewBox="0 0 600 600" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="300" cy="300" r="300" fill="white" />
          </svg>
        </div>
        <div className="absolute left-0 top-0">
          <svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="200" cy="200" r="200" fill="white" />
          </svg>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-20 md:py-28 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <div className="mb-8 inline-block bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-2">
              <span className="text-sm font-medium px-3 py-1 rounded-lg bg-white text-ComidaFacil-primary">
                Novo: Integração com iFood e Rappi!
              </span>
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              Automatize seu delivery via WhatsApp
            </h1>
            
            <p className="text-xl mb-8 md:pr-12 text-white text-opacity-90">
              Aumente suas vendas, reduza custos e melhore a experiência do cliente com nossa plataforma de automação completa para restaurantes e delivery.
            </p>
            
            <div className="space-y-4 md:space-y-0 md:flex items-center gap-4">
              <form onSubmit={handleSubmit} className="flex flex-col md:flex-row gap-3 w-full max-w-md">
                <div className="flex-grow">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Seu e-mail comercial"
                    className="w-full px-4 py-3 rounded-lg text-gray-800 focus:ring-2 focus:ring-ComidaFacil-accent focus:outline-none"
                    required
                  />
                </div>
                <button
                  type="submit"
                  disabled={loading}
                  className="bg-ComidaFacil-accent text-gray-900 font-medium px-6 py-3 rounded-lg hover:bg-opacity-90 transition-colors disabled:opacity-70"
                >
                  {loading ? 'Enviando...' : 'Iniciar Teste Grátis'}
                </button>
              </form>
            </div>
            
            {success && (
              <div className="mt-3 text-white bg-green-500 bg-opacity-20 px-4 py-2 rounded-lg">
                Ótimo! Enviaremos mais informações para o seu e-mail.
              </div>
            )}
            
            <div className="mt-6 text-sm text-white text-opacity-80">
              Não precisa de cartão de crédito. Teste grátis por 14 dias.
            </div>
            
            <div className="mt-12 flex flex-wrap gap-8">
              <div className="flex items-center gap-2">
                <div className="p-1 bg-white bg-opacity-30 rounded-full">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>Configuração em minutos</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="p-1 bg-white bg-opacity-30 rounded-full">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>Suporte 24/7</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="p-1 bg-white bg-opacity-30 rounded-full">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span>Cancelamento a qualquer momento</span>
              </div>
            </div>
          </div>
          
          <div className="flex justify-center">
            <div className="relative w-full max-w-md h-[550px]">
              <Image
                src="/assets/dashboard-preview.png"
                alt="ComidaFacil Dashboard Preview"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 