'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-10">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Image 
              src="/assets/logo.png" 
              alt="ComidaFacil Logo" 
              width={40} 
              height={40} 
              className="w-10 h-10"
            />
            <span className="text-xl font-bold text-ComidaFacil-primary">ComidaFacil</span>
          </Link>

          {/* Desktop Menu */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/#features" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
              Funcionalidades
            </Link>
            <Link href="/#pricing" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
              Preços
            </Link>
            <Link href="/#testimonials" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
              Depoimentos
            </Link>
            <Link href="/#faq" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
              FAQ
            </Link>
          </nav>

          {/* CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/login" className="text-ComidaFacil-primary hover:text-ComidaFacil-secondary transition-colors">
              Entrar
            </Link>
            <Link href="/register" className="btn-primary">
              Cadastre-se
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden text-gray-500 focus:outline-none"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-100">
            <nav className="flex flex-col space-y-4">
              <Link href="/#features" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                Funcionalidades
              </Link>
              <Link href="/#pricing" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                Preços
              </Link>
              <Link href="/#testimonials" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                Depoimentos
              </Link>
              <Link href="/#faq" className="text-gray-600 hover:text-ComidaFacil-primary transition-colors">
                FAQ
              </Link>
              <div className="flex flex-col space-y-2 pt-2 border-t border-gray-100">
                <Link href="/login" className="text-ComidaFacil-primary hover:text-ComidaFacil-secondary transition-colors">
                  Entrar
                </Link>
                <Link href="/register" className="btn-primary text-center">
                  Cadastre-se
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
} 