import { default as makeWASocket, DisconnectReason, useMultiFileAuthState } from '@whiskeysockets/baileys'
import { Boom } from '@hapi/boom'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export class WhatsAppService {
  private socket: any
  private restaurantId: string

  constructor(restaurantId: string) {
    this.restaurantId = restaurantId
  }

  async connect() {
    const { state, saveCreds } = await useMultiFileAuthState(`auth/${this.restaurantId}`)
    
    this.socket = makeWASocket({
      auth: state,
      printQRInTerminal: true,
      defaultQueryTimeoutMs: undefined,
    })

    this.socket.ev.on('connection.update', async (update: any) => {
      const { connection, lastDisconnect } = update

      if (connection === 'close') {
        const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut

        if (shouldReconnect) {
          this.connect()
        }
      } else if (connection === 'open') {
        await saveCreds()
        console.log('WhatsApp conectado!')
      }
    })

    this.socket.ev.on('messages.upsert', async ({ messages }: any) => {
      const msg = messages[0]
      if (!msg.message || msg.key.fromMe) return

      const sender = msg.key.remoteJid
      const messageText = msg.message.conversation || 
                         msg.message.extendedTextMessage?.text || ''

      await this.handleIncomingMessage(sender, messageText)
    })
  }

  private async handleIncomingMessage(sender: string, message: string) {
    const restaurant = await prisma.restaurant.findUnique({
      where: { id: this.restaurantId }
    })

    if (!restaurant) return

    // Lógica de resposta automática
    if (message.toLowerCase().includes('cardápio') || 
        message.toLowerCase().includes('menu') || 
        message.toLowerCase().includes('preço')) {
      await this.sendMenu(sender)
    } else if (message.toLowerCase().includes('horário') || 
               message.toLowerCase().includes('aberto')) {
      await this.sendOpeningHours(sender)
    } else if (message.toLowerCase().includes('fazer pedido') || 
               message.toLowerCase().includes('pedir')) {
      await this.sendOrderInstructions(sender)
    } else {
      await this.sendWelcomeMessage(sender)
    }
  }

  private async sendMenu(sender: string) {
    const menuItems = await prisma.menuItem.findMany({
      where: { restaurantId: this.restaurantId }
    })

    let message = '🍽️ *Nosso Cardápio:*\n\n'
    
    menuItems.forEach(item => {
      message += `*${item.name}*\n`
      message += `${item.description}\n`
      message += `R$ ${item.price.toFixed(2)}\n\n`
    })

    message += '\nPara fazer um pedido, envie "fazer pedido"'

    await this.socket.sendMessage(sender, { text: message })
  }

  private async sendOpeningHours(sender: string) {
    const message = '⏰ *Horário de Funcionamento:*\n\n' +
                   'Segunda a Sexta: 11h às 22h\n' +
                   'Sábado e Domingo: 11h às 23h\n\n' +
                   'Para ver nosso cardápio, envie "cardápio"'

    await this.socket.sendMessage(sender, { text: message })
  }

  private async sendOrderInstructions(sender: string) {
    const message = '🛍️ *Como Fazer seu Pedido:*\n\n' +
                   '1. Escolha os itens do cardápio\n' +
                   '2. Envie o número do item e a quantidade\n' +
                   '3. Confirme seu endereço de entrega\n' +
                   '4. Escolha a forma de pagamento\n\n' +
                   'Para ver nosso cardápio, envie "cardápio"'

    await this.socket.sendMessage(sender, { text: message })
  }

  private async sendWelcomeMessage(sender: string) {
    const message = '👋 *Bem-vindo ao ComidaFacil!*\n\n' +
                   'Como posso ajudar você hoje?\n\n' +
                   '• Envie "cardápio" para ver nosso menu\n' +
                   '• Envie "horário" para ver nossos horários\n' +
                   '• Envie "fazer pedido" para iniciar um pedido'

    await this.socket.sendMessage(sender, { text: message })
  }

  async sendOrderConfirmation(orderId: string) {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        items: {
          include: {
            menuItem: true
          }
        }
      }
    })

    if (!order) return

    let message = '✅ *Pedido Confirmado!*\n\n'
    message += `*Pedido #${orderId.slice(-6)}*\n\n`
    message += '*Itens:*\n`

    order.items.forEach(item => {
      message += `${item.quantity}x ${item.menuItem.name}\n`
      message += `R$ ${(item.price * item.quantity).toFixed(2)}\n\n`
    })

    message += `*Total: R$ ${order.total.toFixed(2)}*\n\n`
    message += '*Endereço de Entrega:*\n'
    message += `${order.customerAddress}\n\n`
    message += 'Agradecemos seu pedido! 🍽️'

    await this.socket.sendMessage(order.customerPhone, { text: message })
  }

  async sendDeliveryUpdate(orderId: string, status: string) {
    const order = await prisma.order.findUnique({
      where: { id: orderId }
    })

    if (!order) return

    let message = '🔄 *Atualização do Pedido*\n\n'
    message += `*Pedido #${orderId.slice(-6)}*\n`
    message += `Status: ${status}\n\n`
    message += 'Agradecemos sua preferência! 🍽️'

    await this.socket.sendMessage(order.customerPhone, { text: message })
  }
} 