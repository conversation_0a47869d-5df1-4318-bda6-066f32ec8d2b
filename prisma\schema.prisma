generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Restaurant {
  id          String   @id @default(cuid())
  name        String
  whatsapp    String
  address     String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  menu        MenuItem[]
  orders      Order[]
}

model MenuItem {
  id          String   @id @default(cuid())
  name        String
  description String
  price       Float
  image       String?
  category    String
  restaurant  Restaurant @relation(fields: [restaurantId], references: [id])
  restaurantId String
  orderItems  OrderItem[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Order {
  id          String   @id @default(cuid())
  restaurant  Restaurant @relation(fields: [restaurantId], references: [id])
  restaurantId String
  customerName String
  customerPhone String
  customerAddress String
  items       OrderItem[]
  total       Float
  status      OrderStatus @default(PENDING)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model OrderItem {
  id          String   @id @default(cuid())
  order       Order    @relation(fields: [orderId], references: [id])
  orderId     String
  menuItem    MenuItem @relation(fields: [menuItemId], references: [id])
  menuItemId  String
  quantity    Int
  price       Float
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PREPARING
  READY
  DELIVERING
  DELIVERED
  CANCELLED
} 