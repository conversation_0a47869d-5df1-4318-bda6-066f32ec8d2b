import { Hero } from '@/components/Hero';
import { FeaturesSection } from '@/components/Features';
import { TestimonialSection } from '@/components/Testimonial';
import { PricingSection } from '@/components/Pricing';
import { FaqSection } from '@/components/Faq';

export default function Home() {
  return (
    <>
      <Hero />
      <FeaturesSection />
      <TestimonialSection />
      <PricingSection />
      <FaqSection />
      
      {/* CTA Section */}
      <section className="bg-ComidaFacil-primary text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Pronto para transformar seu delivery?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Automatize seu atendimento, aumente suas vendas e melhore a experiência dos seus clientes com o ComidaFacil.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <a 
              href="/register" 
              className="px-6 py-3 bg-white text-ComidaFacil-primary font-medium rounded-lg hover:bg-gray-100 transition-colors"
            >
              Começar Agora
            </a>
            <a 
              href="/contact" 
              className="px-6 py-3 border-2 border-white text-white font-medium rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors"
            >
              Falar com Consultor
            </a>
          </div>
        </div>
      </section>
    </>
  );
} 