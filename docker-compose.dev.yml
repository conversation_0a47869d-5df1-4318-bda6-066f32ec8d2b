version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - DATABASE_URL=**************************************/comidafacil
      - NODE_ENV=development
    depends_on:
      - db
    command: npm run dev

  db:
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=comidafacil
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data: 