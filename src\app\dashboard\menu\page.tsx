'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

interface MenuItem {
  id: string
  name: string
  description: string
  price: number
  image?: string
  category: string
}

export default function MenuManagement() {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [newItem, setNewItem] = useState<Partial<MenuItem>>({
    name: '',
    description: '',
    price: 0,
    category: ''
  })
  const [isEditing, setIsEditing] = useState(false)

  useEffect(() => {
    fetchMenuItems()
  }, [])

  const fetchMenuItems = async () => {
    try {
      const response = await fetch('/api/menu')
      const data = await response.json()
      setMenuItems(data)
      // Extrai categorias únicas
      const uniqueCategories = [...new Set(data.map((item: MenuItem) => item.category))]
      setCategories(uniqueCategories)
    } catch (error) {
      console.error('Error fetching menu items:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await fetch('/api/menu', {
        method: isEditing ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newItem),
      })
      
      if (response.ok) {
        fetchMenuItems()
        setNewItem({
          name: '',
          description: '',
          price: 0,
          category: ''
        })
        setIsEditing(false)
      }
    } catch (error) {
      console.error('Error saving menu item:', error)
    }
  }

  const handleEdit = (item: MenuItem) => {
    setNewItem(item)
    setIsEditing(true)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Tem certeza que deseja excluir este item?')) return

    try {
      const response = await fetch(`/api/menu/${id}`, {
        method: 'DELETE',
      })
      
      if (response.ok) {
        fetchMenuItems()
      }
    } catch (error) {
      console.error('Error deleting menu item:', error)
    }
  }

  return (
    <div className="min-h-screen bg-ComidaFacil-background py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Gerenciar Cardápio</h1>

        {/* Formulário */}
        <div className="card mb-8">
          <h2 className="text-xl font-semibold mb-4">
            {isEditing ? 'Editar Item' : 'Adicionar Novo Item'}
          </h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-gray-700 mb-2">Nome</label>
              <input
                type="text"
                className="input"
                value={newItem.name}
                onChange={(e) => setNewItem({...newItem, name: e.target.value})}
                required
              />
            </div>
            <div>
              <label className="block text-gray-700 mb-2">Descrição</label>
              <textarea
                className="input"
                value={newItem.description}
                onChange={(e) => setNewItem({...newItem, description: e.target.value})}
                required
              />
            </div>
            <div>
              <label className="block text-gray-700 mb-2">Preço</label>
              <input
                type="number"
                className="input"
                value={newItem.price}
                onChange={(e) => setNewItem({...newItem, price: Number(e.target.value)})}
                required
                min="0"
                step="0.01"
              />
            </div>
            <div>
              <label className="block text-gray-700 mb-2">Categoria</label>
              <input
                type="text"
                className="input"
                value={newItem.category}
                onChange={(e) => setNewItem({...newItem, category: e.target.value})}
                required
              />
            </div>
            <div className="flex gap-4">
              <button type="submit" className="btn-primary">
                {isEditing ? 'Salvar Alterações' : 'Adicionar Item'}
              </button>
              {isEditing && (
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={() => {
                    setIsEditing(false)
                    setNewItem({
                      name: '',
                      description: '',
                      price: 0,
                      category: ''
                    })
                  }}
                >
                  Cancelar
                </button>
              )}
            </div>
          </form>
        </div>

        {/* Lista de Itens */}
        <div className="card">
          <h2 className="text-xl font-semibold mb-4">Itens do Cardápio</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {menuItems.map((item) => (
              <div key={item.id} className="border rounded-lg p-4">
                {item.image && (
                  <div className="relative h-48 mb-4">
                    <Image
                      src={item.image}
                      alt={item.name}
                      fill
                      className="object-cover rounded-lg"
                    />
                  </div>
                )}
                <h3 className="text-lg font-semibold mb-2">{item.name}</h3>
                <p className="text-gray-600 mb-2">{item.description}</p>
                <p className="text-ComidaFacil-primary font-bold mb-4">
                  R$ {item.price.toFixed(2)}
                </p>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEdit(item)}
                    className="btn-secondary flex-1"
                  >
                    Editar
                  </button>
                  <button
                    onClick={() => handleDelete(item.id)}
                    className="btn-primary bg-red-500 hover:bg-red-600 flex-1"
                  >
                    Excluir
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
} 