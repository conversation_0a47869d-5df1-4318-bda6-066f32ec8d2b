import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET() {
  try {
    // TODO: Adicionar autenticação e pegar o restaurantId do usuário logado
    const restaurantId = 'test-restaurant-id'

    const orders = await prisma.order.findMany({
      where: { restaurantId },
      include: {
        items: {
          include: {
            menuItem: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 50 // Limita aos 50 pedidos mais recentes
    })

    return NextResponse.json(orders)
  } catch (error) {
    console.error('Error fetching orders:', error)
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const {
      restaurantId,
      customerName,
      customerPhone,
      customerAddress,
      items,
      total
    } = await request.json()

    // Cria o pedido
    const order = await prisma.order.create({
      data: {
        restaurantId,
        customerName,
        customerPhone,
        customerAddress,
        total,
        items: {
          create: items.map((item: any) => ({
            menuItemId: item.menuItemId,
            quantity: item.quantity,
            price: item.price
          }))
        }
      },
      include: {
        items: {
          include: {
            menuItem: true
          }
        }
      }
    })

    // TODO: Enviar confirmação via WhatsApp
    // const whatsappService = new WhatsAppService(restaurantId)
    // await whatsappService.sendOrderConfirmation(order.id)

    return NextResponse.json(order)
  } catch (error) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    )
  }
} 