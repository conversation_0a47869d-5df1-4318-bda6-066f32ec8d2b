# Guia de Contribuição

## 🚀 Começando

1. Clone o repositório:
```bash
git clone https://github.com/seu-usuario/ComidaFacil.git
cd ComidaFacil
```

2. Instale as dependências:
```bash
npm install
```

3. Configure as variáveis de ambiente:
```bash
cp .env.example .env.local
```

4. Inicie o banco de dados e a aplicação com Docker:
```bash
docker-compose -f docker-compose.dev.yml up -d
```

5. Execute as migrações do Prisma:
```bash
npx prisma migrate dev
```

6. Acesse a aplicação em `http://localhost:3000`

## 🛠️ Desenvolvimento

### Estrutura do Projeto

```
ComidaFacil/
├── src/
│   ├── app/           # Páginas e rotas da API
│   ├── components/    # Componentes React reutilizáveis
│   ├── services/      # Serviços e integrações
│   ├── utils/         # Funções utilitárias
│   └── lib/           # Configurações e bibliotecas
├── prisma/            # Schema e migrações do banco de dados
└── public/            # Arquivos estáticos
```

### Convenções de Código

- Use TypeScript para todo novo código
- Siga as convenções de estilo do ESLint
- Escreva testes para novas funcionalidades
- Documente APIs e componentes complexos
- Use commits semânticos

### Fluxo de Trabalho

1. Crie uma branch para sua feature:
```bash
git checkout -b feature/nome-da-feature
```

2. Faça commit das suas alterações:
```bash
git commit -m "feat: adiciona nova funcionalidade"
```

3. Push para a branch:
```bash
git push origin feature/nome-da-feature
```

4. Abra um Pull Request

## 📝 Testes

Execute os testes:
```bash
npm test
```

## 🔍 Code Review

- Revise o código antes de submeter PRs
- Verifique se os testes passam
- Certifique-se de que o código segue as convenções
- Atualize a documentação quando necessário

## 🚀 Deploy

O deploy é feito automaticamente via GitHub Actions quando um PR é mergeado na branch `main`.

## 📚 Documentação

- Mantenha o README.md atualizado
- Documente novas APIs
- Atualize o schema do banco de dados
- Adicione exemplos de uso quando relevante

## 🤝 Suporte

Para suporte, abra uma issue no GitHub ou entre em contato com a equipe de desenvolvimento. 