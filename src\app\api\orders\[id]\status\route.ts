import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { WhatsAppService } from '@/services/whatsapp'

const prisma = new PrismaClient()

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { status } = await request.json()
    const orderId = params.id

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      )
    }

    // Atualiza o status do pedido
    const order = await prisma.order.update({
      where: { id: orderId },
      data: { status },
      include: {
        restaurant: true
      }
    })

    // Envia atualização via WhatsApp
    const whatsappService = new WhatsAppService(order.restaurantId)
    await whatsappService.sendDeliveryUpdate(orderId, status)

    return NextResponse.json(order)
  } catch (error) {
    console.error('Order status update error:', error)
    return NextResponse.json(
      { error: 'Failed to update order status' },
      { status: 500 }
    )
  }
} 