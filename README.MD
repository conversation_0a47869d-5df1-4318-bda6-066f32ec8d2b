# 🍽️ ComidaFacil

<div align="center">
  <img src="/public/assets/logo.png" alt="ComidaFacil Logo" width="300"/>
  <p><strong>Automação de Delivery via WhatsApp</strong></p>
  
  [![Next.js](https://img.shields.io/badge/Next.js-000000?style=for-the-badge&logo=next.js&logoColor=white)](https://nextjs.org/)
  [![WhatsApp](https://img.shields.io/badge/WhatsApp-25D366?style=for-the-badge&logo=whatsapp&logoColor=white)](https://github.com/WhiskeySockets/Baileys)
  [![PostgreSQL](https://img.shields.io/badge/PostgreSQL-4169E1?style=for-the-badge&logo=postgresql&logoColor=white)](https://www.postgresql.org/)
  [![Docker](https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white)](https://www.docker.com/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)
</div>

## 📋 Índice

- [📖 Sobre o Projeto](#-sobre-o-projeto)
- [✨ Funcionalidades](#-funcionalidades)
- [🚀 Stack Tecnológica](#-stack-tecnológica)
- [📱 Fluxo de Vendas](#-fluxo-de-vendas)
- [⚙️ Configuração do Ambiente](#️-configuração-do-ambiente)
- [📂 Estrutura do Projeto](#-estrutura-do-projeto)
- [🔒 Segurança](#-segurança)
- [📱 Responsividade](#-responsividade)
- [👥 Contribuição](#-contribuição)
- [📄 Licença](#-licença)
- [📞 Suporte](#-suporte)

## 📖 Sobre o Projeto

**ComidaFacil** é uma solução SaaS (Software as a Service) que automatiza o processo de vendas e atendimento via WhatsApp para estabelecimentos de delivery. Utilizando a biblioteca Baileys e Next.js, oferecemos uma experiência fluida tanto para clientes quanto para restaurantes.

## ✨ Funcionalidades

<div align="center">
  <table>
    <tr>
      <td width="50%">
        <h3>🍽️ Para Clientes</h3>
        <ul>
          <li>Interface web moderna para leitura do QR Code do WhatsApp</li>
          <li>Cardápio digital interativo</li>
          <li>Sistema de checkout integrado</li>
          <li>Acompanhamento do status do pedido</li>
          <li>Previsão de entrega em tempo real</li>
        </ul>
      </td>
      <td width="50%">
        <h3>🏪 Para Restaurantes</h3>
        <ul>
          <li>Automação completa do atendimento via WhatsApp</li>
          <li>Dashboard administrativo</li>
          <li>Gestão de cardápio</li>
          <li>Relatórios de vendas</li>
          <li>Integração com sistemas de delivery</li>
        </ul>
      </td>
    </tr>
  </table>
</div>

## 🚀 Stack Tecnológica

<div align="center">
  <table>
    <tr>
      <td><strong>Frontend</strong></td>
      <td>Next.js</td>
    </tr>
    <tr>
      <td><strong>WhatsApp API</strong></td>
      <td>Baileys</td>
    </tr>
    <tr>
      <td><strong>Banco de Dados</strong></td>
      <td>PostgreSQL</td>
    </tr>
    <tr>
      <td><strong>Autenticação</strong></td>
      <td>NextAuth.js</td>
    </tr>
    <tr>
      <td><strong>UI Framework</strong></td>
      <td>Tailwind CSS</td>
    </tr>
    <tr>
      <td><strong>Deploy</strong></td>
      <td>Docker</td>
    </tr>
  </table>
</div>

## 📱 Fluxo de Vendas

<div align="center">
  <img src="https://via.placeholder.com/800x200/1a1a2e/ffffff?text=Fluxo+de+Vendas" alt="Fluxo de Vendas" width="100%"/>
</div>

### 1. 👋 Início da Interação
- Cliente envia mensagem inicial
- Bot responde com orientações para realizar o pedido

### 2. 🔍 Navegação
- Cliente é direcionado para o cardápio digital
- Interface intuitiva para seleção de itens

### 3. 🛒 Checkout
- Processo de finalização de pedido
- Confirmação de endereço e forma de pagamento

### 4. ✅ Finalização
- Envio automático do pedido completo via WhatsApp
- Informação da previsão de entrega

## ⚙️ Configuração do Ambiente

```bash
# Instalação das dependências
npm install

# Configuração das variáveis de ambiente
cp .env.example .env.local

# Inicialização do projeto
npm run dev
```

## 📂 Estrutura do Projeto

```
ComidaFacil/
├── src/
│   ├── components/  # Componentes reutilizáveis
│   ├── pages/       # Páginas da aplicação
│   ├── services/    # Serviços e integrações
│   └── utils/       # Funções utilitárias
├── public/
│   └── assets/      # Imagens e recursos estáticos
└── prisma/
    └── schema.prisma # Esquema do banco de dados
```

## 🔒 Segurança

<div align="center">
  <table>
    <tr>
      <td>🔐 Autenticação em duas etapas</td>
      <td>🔒 Criptografia de dados sensíveis</td>
    </tr>
    <tr>
      <td>📑 Conformidade com LGPD</td>
      <td>🛡️ Proteção contra ataques comuns</td>
    </tr>
  </table>
</div>

## 📱 Responsividade

<div align="center">
  <table>
    <tr>
      <td>📱 Interface adaptativa para todos os dispositivos</td>
      <td>📲 Experiência otimizada para mobile</td>
    </tr>
    <tr>
      <td colspan="2">🖥️ Suporte a diferentes resoluções</td>
    </tr>
  </table>
</div>

## 👥 Contribuição

Contribuições são bem-vindas! Por favor, leia nosso guia de contribuição antes de submeter pull requests.

<div align="center">
  [![Contribua](https://img.shields.io/badge/Contribua-4B32C3?style=for-the-badge)](https://github.com/seuprojeto/ComidaFacil/blob/main/CONTRIBUTING.md)
</div>

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

<div align="center">
  [![Licença MIT](https://img.shields.io/badge/Licença-MIT-yellow.svg?style=for-the-badge)](LICENSE)
</div>

## 📞 Suporte

<div align="center">
  <p>Para suporte, envie um email para:</p>
  
  [![Email](https://img.shields.io/badge/<EMAIL>-D14836?style=for-the-badge&logo=gmail&logoColor=white)](mailto:<EMAIL>)
  
  <p>Ou abra uma issue no repositório:</p>
  
  [![GitHub Issues](https://img.shields.io/badge/GitHub-Issues-181717?style=for-the-badge&logo=github&logoColor=white)](https://github.com/seuprojeto/ComidaFacil/issues)
</div>

---
<div align="center">
  <p>Desenvolvido com ❤️ pela equipe ComidaFacil</p>
</div>
