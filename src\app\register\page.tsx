'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface FormData {
  restaurantName: string
  whatsapp: string
  address: string
  email: string
  password: string
}

export default function Register() {
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState<FormData>({
    restaurantName: '',
    whatsapp: '',
    address: '',
    email: '',
    password: '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (step === 1) {
      setStep(2)
    } else {
      // TODO: Implementar registro
      console.log('Form submitted:', formData)
    }
  }

  return (
    <div className="min-h-screen bg-ComidaFacil-background py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <Image
              src="/assets/logo.png"
              alt="ComidaFacil Logo"
              width={120}
              height={120}
              className="mx-auto mb-4"
            />
            <h1 className="text-3xl font-bold mb-2">Criar sua conta</h1>
            <p className="text-gray-600">
              {step === 1 
                ? 'Preencha os dados do seu restaurante'
                : 'Configure seu WhatsApp para começar'}
            </p>
          </div>

          <div className="card">
            <form onSubmit={handleSubmit}>
              {step === 1 ? (
                <>
                  <div className="mb-4">
                    <label className="block text-gray-700 mb-2">
                      Nome do Restaurante
                    </label>
                    <input
                      type="text"
                      className="input"
                      value={formData.restaurantName}
                      onChange={(e) => setFormData({...formData, restaurantName: e.target.value})}
                      required
                    />
                  </div>
                  <div className="mb-4">
                    <label className="block text-gray-700 mb-2">
                      WhatsApp
                    </label>
                    <input
                      type="tel"
                      className="input"
                      value={formData.whatsapp}
                      onChange={(e) => setFormData({...formData, whatsapp: e.target.value})}
                      placeholder="(00) 00000-0000"
                      required
                    />
                  </div>
                  <div className="mb-4">
                    <label className="block text-gray-700 mb-2">
                      Endereço
                    </label>
                    <input
                      type="text"
                      className="input"
                      value={formData.address}
                      onChange={(e) => setFormData({...formData, address: e.target.value})}
                      required
                    />
                  </div>
                  <div className="mb-4">
                    <label className="block text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      className="input"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      required
                    />
                  </div>
                  <div className="mb-6">
                    <label className="block text-gray-700 mb-2">
                      Senha
                    </label>
                    <input
                      type="password"
                      className="input"
                      value={formData.password}
                      onChange={(e) => setFormData({...formData, password: e.target.value})}
                      required
                    />
                  </div>
                </>
              ) : (
                <div className="text-center">
                  <p className="mb-6">
                    Escaneie o QR Code abaixo com seu WhatsApp para conectar sua conta
                  </p>
                  <div className="bg-white p-4 rounded-lg inline-block">
                    {/* TODO: Implementar QR Code do WhatsApp */}
                    <div className="w-64 h-64 bg-gray-200 rounded-lg"></div>
                  </div>
                </div>
              )}

              <div className="flex justify-between">
                {step === 2 && (
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={() => setStep(1)}
                  >
                    Voltar
                  </button>
                )}
                <button
                  type="submit"
                  className={`btn-primary ${step === 2 ? 'ml-auto' : ''}`}
                >
                  {step === 1 ? 'Próximo' : 'Finalizar'}
                </button>
              </div>
            </form>
          </div>

          <p className="text-center mt-6 text-gray-600">
            Já tem uma conta?{' '}
            <Link href="/login" className="text-ComidaFacil-primary hover:underline">
              Faça login
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
} 