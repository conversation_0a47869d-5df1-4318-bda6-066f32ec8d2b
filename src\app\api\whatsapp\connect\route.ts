import { NextResponse } from 'next/server'
import { WhatsAppService } from '@/services/whatsapp'

export async function POST(request: Request) {
  try {
    const { restaurantId } = await request.json()

    if (!restaurantId) {
      return NextResponse.json(
        { error: 'Restaurant ID is required' },
        { status: 400 }
      )
    }

    const whatsappService = new WhatsAppService(restaurantId)
    await whatsappService.connect()

    return NextResponse.json({ message: 'WhatsApp connection initiated' })
  } catch (error) {
    console.error('WhatsApp connection error:', error)
    return NextResponse.json(
      { error: 'Failed to connect WhatsApp' },
      { status: 500 }
    )
  }
} 